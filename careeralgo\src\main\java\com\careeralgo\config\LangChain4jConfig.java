package com.careeralgo.config;

import dev.langchain4j.model.chat.ChatModel;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.embedding.AllMiniLmL6V2EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import dev.langchain4j.data.segment.TextSegment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.Duration;

/**
 * Configuration for LangChain4j AI components
 */
@Configuration
public class LangChain4jConfig {

    @Value("${careeralgo.ai.openai.api-key}")
    private String openAiApiKey;

    @Value("${careeralgo.ai.openai.model:gpt-4}")
    private String primaryModel;

    @Value("${careeralgo.ai.openai.fallback-model:gpt-3.5-turbo}")
    private String fallbackModel;

    @Value("${careeralgo.ai.openai.timeout:60}")
    private int timeoutSeconds;

    @Value("${careeralgo.ai.openai.max-retries:3}")
    private int maxRetries;

    @Value("${careeralgo.ai.openai.temperature:0.7}")
    private double temperature;

    @Value("${careeralgo.ai.openai.max-tokens:2000}")
    private int maxTokens;

    /**
     * Primary ChatModel for AI agents
     */
    @Bean
    @Primary
    public ChatModel primaryChatModel() {
        return OpenAiChatModel.builder()
                .apiKey(openAiApiKey)
                .modelName(primaryModel)
                .timeout(Duration.ofSeconds(timeoutSeconds))
                .maxRetries(maxRetries)
                .temperature(temperature)
                .maxTokens(maxTokens)
                .logRequests(true)
                .logResponses(false) // Avoid logging sensitive data
                .build();
    }

    /**
     * Fallback ChatModel for high availability
     */
    @Bean("fallbackChatModel")
    public ChatModel fallbackChatModel() {
        return OpenAiChatModel.builder()
                .apiKey(openAiApiKey)
                .modelName(fallbackModel)
                .timeout(Duration.ofSeconds(30))
                .maxRetries(2)
                .temperature(temperature)
                .maxTokens(1500)
                .build();
    }

    /**
     * Embedding model for semantic search and similarity
     */
    @Bean
    public EmbeddingModel embeddingModel() {
        return new AllMiniLmL6V2EmbeddingModel();
    }

    /**
     * In-memory embedding store for career-related content
     */
    @Bean
    public EmbeddingStore<TextSegment> embeddingStore() {
        return new InMemoryEmbeddingStore<>();
    }

    /**
     * Specialized ChatModel for resume analysis
     */
    @Bean("resumeAnalysisChatModel")
    public ChatModel resumeAnalysisChatModel() {
        return OpenAiChatModel.builder()
                .apiKey(openAiApiKey)
                .modelName(primaryModel)
                .timeout(Duration.ofSeconds(90)) // Longer timeout for complex analysis
                .maxRetries(maxRetries)
                .temperature(0.3) // Lower temperature for more consistent analysis
                .maxTokens(3000) // Higher token limit for detailed analysis
                .build();
    }

    /**
     * Specialized ChatModel for job matching
     */
    @Bean("jobMatchingChatModel")
    public ChatModel jobMatchingChatModel() {
        return OpenAiChatModel.builder()
                .apiKey(openAiApiKey)
                .modelName(primaryModel)
                .timeout(Duration.ofSeconds(60))
                .maxRetries(maxRetries)
                .temperature(0.5) // Balanced temperature for matching
                .maxTokens(2500)
                .build();
    }

    /**
     * Specialized ChatModel for interview preparation
     */
    @Bean("interviewPrepChatModel")
    public ChatModel interviewPrepChatModel() {
        return OpenAiChatModel.builder()
                .apiKey(openAiApiKey)
                .modelName(primaryModel)
                .timeout(Duration.ofSeconds(45))
                .maxRetries(maxRetries)
                .temperature(0.6) // Slightly higher for creative feedback
                .maxTokens(2000)
                .build();
    }

    /**
     * Specialized ChatModel for career advice
     */
    @Bean("careerAdviceChatModel")
    public ChatModel careerAdviceChatModel() {
        return OpenAiChatModel.builder()
                .apiKey(openAiApiKey)
                .modelName(primaryModel)
                .timeout(Duration.ofSeconds(75))
                .maxRetries(maxRetries)
                .temperature(0.8) // Higher temperature for creative advice
                .maxTokens(2500)
                .build();
    }
}
