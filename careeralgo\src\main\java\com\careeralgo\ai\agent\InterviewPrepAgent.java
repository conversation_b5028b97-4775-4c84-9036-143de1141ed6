package com.careeralgo.ai.agent;

import dev.langchain4j.service.AiService;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;
import dev.langchain4j.service.MemoryId;
import reactor.core.publisher.Flux;

import java.util.concurrent.CompletableFuture;
import java.util.List;
import java.util.Map;

/**
 * Modern AI agent for interview preparation using LangChain4j
 */
@AiService
public interface InterviewPrepAgent {

    @SystemMessage({
            "You are an expert interview preparation agent with deep expertise in:",
            "- Interview strategy and best practices across industries",
            "- Behavioral interview techniques (STAR method)",
            "- Technical interview preparation and coding challenges",
            "- Executive and leadership interview dynamics",
            "- Company culture assessment and fit evaluation",
            "",
            "INTERVIEW TYPES EXPERTISE:",
            "- Behavioral: Situational questions and competency assessment",
            "- Technical: Role-specific skills and problem-solving",
            "- Case Study: Business analysis and strategic thinking",
            "- Panel: Multi-interviewer dynamics and coordination",
            "- Video: Remote interview best practices",
            "",
            "EVALUATION FRAMEWORK:",
            "- Content Quality: Relevance, depth, and accuracy (30%)",
            "- Communication: Clarity, confidence, and engagement (25%)",
            "- Structure: Organization using STAR or similar methods (20%)",
            "- Authenticity: Genuine examples and personal insights (15%)",
            "- Professional Presence: Demeanor and interview etiquette (10%)",
            "",
            "COACHING APPROACH:",
            "- Provide specific, actionable feedback",
            "- Suggest alternative phrasings and improvements",
            "- Identify strengths to leverage and areas to develop",
            "- Offer practice scenarios and follow-up questions",
            "- Build confidence through structured preparation"
    })
    InterviewEvaluationResult evaluateInterviewAnswer(
            @UserMessage("Evaluate this interview answer: {{answer}} for question: {{question}}")
            String answer,
            @V("question") String question,
            @V("questionType") String questionType,
            @V("targetRole") String targetRole);

    @SystemMessage({
            "Generate relevant interview questions based on job description and role requirements.",
            "Include behavioral, technical, and situational questions with varying difficulty levels."
    })
    InterviewQuestionSet generateInterviewQuestions(
            @UserMessage String jobDescription,
            @V("experienceLevel") String experienceLevel,
            @V("interviewType") String interviewType,
            @V("questionCount") int questionCount);

    @SystemMessage({
            "Provide comprehensive interview preparation strategy and tips.",
            "Include company research, question preparation, and presentation advice."
    })
    InterviewPrepStrategy createPrepStrategy(
            @UserMessage String candidateProfile,
            @V("jobDescription") String jobDescription,
            @V("companyInfo") String companyInfo,
            @V("interviewFormat") String interviewFormat);

    @SystemMessage({
            "Conduct a mock interview session with dynamic follow-up questions.",
            "Adapt questions based on candidate responses and provide real-time guidance."
    })
    Flux<MockInterviewUpdate> conductMockInterview(
            @UserMessage String candidateProfile,
            @V("jobDescription") String jobDescription,
            @MemoryId String sessionId);

    @SystemMessage({
            "Analyze interview performance across multiple sessions.",
            "Track improvement over time and identify development patterns."
    })
    CompletableFuture<InterviewProgressAnalysis> analyzeInterviewProgress(
            @UserMessage("Analyze interview progress: {{sessionHistory}}")
            List<String> sessionHistory,
            @MemoryId String candidateId);

    @SystemMessage({
            "Generate personalized STAR method examples based on candidate experience.",
            "Help structure responses for maximum impact and clarity."
    })
    STARMethodExamples generateSTARExamples(
            @UserMessage String candidateExperience,
            @V("targetCompetencies") List<String> targetCompetencies);

    @SystemMessage({
            "Provide company-specific interview insights and preparation tips.",
            "Include culture, values, common questions, and interview process details."
    })
    CompanyInterviewInsights getCompanyInsights(
            @UserMessage String companyName,
            @V("targetRole") String targetRole,
            @V("interviewType") String interviewType);

    @SystemMessage({
            "Create a comprehensive interview follow-up strategy.",
            "Include thank you notes, additional information, and next steps."
    })
    InterviewFollowUpPlan createFollowUpPlan(
            @UserMessage String interviewSummary,
            @V("interviewerInfo") String interviewerInfo,
            @V("discussedTopics") List<String> discussedTopics);

    // Supporting data structures
    record InterviewEvaluationResult(
            int overallScore,
            int contentQualityScore,
            int communicationScore,
            int structureScore,
            int authenticityScore,
            int professionalPresenceScore,
            List<String> strengths,
            List<String> improvements,
            List<String> suggestions,
            String improvedAnswer,
            String feedback) {}

    record InterviewQuestionSet(
            List<BehavioralQuestion> behavioralQuestions,
            List<TechnicalQuestion> technicalQuestions,
            List<SituationalQuestion> situationalQuestions,
            List<String> companySpecificQuestions,
            String preparationTips) {}

    record BehavioralQuestion(
            String question,
            String competency,
            String difficulty,
            List<String> keyPoints,
            String starGuidance) {}

    record TechnicalQuestion(
            String question,
            String skillArea,
            String difficulty,
            List<String> expectedElements,
            String evaluationCriteria) {}

    record SituationalQuestion(
            String scenario,
            String challenge,
            List<String> considerations,
            String idealApproach) {}

    record InterviewPrepStrategy(
            String overallStrategy,
            List<String> researchAreas,
            List<String> practiceTopics,
            List<String> questionsToAsk,
            List<String> presentationTips,
            String timeline,
            Map<String, String> resources) {}

    record MockInterviewUpdate(
            String updateType,
            String question,
            String guidance,
            String feedback,
            int progressPercentage,
            String timestamp) {}

    record InterviewProgressAnalysis(
            String progressSummary,
            List<String> improvements,
            List<String> consistentStrengths,
            List<String> developmentAreas,
            String readinessAssessment,
            List<String> nextSteps) {}

    record STARMethodExamples(
            List<STARExample> examples,
            String structureGuidance,
            List<String> improvementTips) {}

    record STARExample(
            String competency,
            String situation,
            String task,
            String action,
            String result,
            String impact) {}

    record CompanyInterviewInsights(
            String companyOverview,
            String cultureDescription,
            List<String> commonQuestions,
            List<String> interviewTips,
            String processDescription,
            List<String> successFactors) {}

    record InterviewFollowUpPlan(
            String thankYouTemplate,
            List<String> additionalInformation,
            String timeline,
            List<String> nextSteps,
            String professionalTips) {}
}
