package com.careeralgo.ai.orchestrator;

import com.careeralgo.ai.agent.*;
import com.careeralgo.ai.tools.CareerToolProvider;
import dev.langchain4j.memory.chat.MessageWindowChatMemory;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.service.AiServices;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Modern AI Agent Orchestrator using LangChain4j patterns
 * Coordinates multiple specialized agents for comprehensive career assistance
 */
@Component
public class ModernAIAgentOrchestrator {

    private static final Logger logger = LoggerFactory.getLogger(ModernAIAgentOrchestrator.class);

    private final ChatLanguageModel primaryModel;
    private final ChatLanguageModel fallbackModel;
    private final Executor agentExecutor;
    private final Map<String, AgentMetrics> agentMetrics;
    private final CareerToolProvider toolProvider;

    // Agent instances
    private final ResumeAnalysisAgent resumeAnalysisAgent;
    private final JobMatchingAgent jobMatchingAgent;
    private final InterviewPrepAgent interviewPrepAgent;
    private final CareerAdvisorAgent careerAdvisorAgent;

    public ModernAIAgentOrchestrator(
            @Value("${careeralgo.ai.openai.api-key}") String apiKey,
            @Value("${careeralgo.ai.openai.model:gpt-4}") String primaryModelName,
            @Value("${careeralgo.ai.openai.fallback-model:gpt-3.5-turbo}") String fallbackModelName,
            CareerToolProvider toolProvider) {

        this.toolProvider = toolProvider;

        // Initialize primary model with advanced configuration
        this.primaryModel = OpenAiChatModel.builder()
                .apiKey(apiKey)
                .modelName(primaryModelName)
                .timeout(Duration.ofSeconds(60))
                .maxRetries(3)
                .temperature(0.7)
                .maxTokens(2000)
                .logRequests(true)
                .logResponses(false) // Avoid logging sensitive data
                .build();

        // Initialize fallback model
        this.fallbackModel = OpenAiChatModel.builder()
                .apiKey(apiKey)
                .modelName(fallbackModelName)
                .timeout(Duration.ofSeconds(30))
                .maxRetries(2)
                .temperature(0.7)
                .maxTokens(1500)
                .build();

        this.agentExecutor = Executors.newVirtualThreadPerTaskExecutor();
        this.agentMetrics = new ConcurrentHashMap<>();

        // Initialize specialized agents
        this.resumeAnalysisAgent = createResumeAnalysisAgent();
        this.jobMatchingAgent = createJobMatchingAgent();
        this.interviewPrepAgent = createInterviewPrepAgent();
        this.careerAdvisorAgent = createCareerAdvisorAgent();

        logger.info("Modern AI Agent Orchestrator initialized with {} agents", 4);
    }

    /**
     * Process comprehensive career assistance request
     */
    public CompletableFuture<CareerAssistanceResponse> processCareerRequest(CareerAssistanceRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                logger.info("Processing career assistance request: {}", request.getRequestType());

                return switch (request.getRequestType()) {
                    case RESUME_ANALYSIS -> processResumeAnalysis(request);
                    case JOB_MATCHING -> processJobMatching(request);
                    case INTERVIEW_PREP -> processInterviewPrep(request);
                    case CAREER_ADVICE -> processCareerAdvice(request);
                    case COMPREHENSIVE_ANALYSIS -> processComprehensiveAnalysis(request);
                    default -> throw new IllegalArgumentException("Unsupported request type: " + request.getRequestType());
                };

            } catch (Exception e) {
                logger.error("Error processing career request", e);
                return CareerAssistanceResponse.error("Failed to process request: " + e.getMessage());
            }
        }, agentExecutor);
    }

    /**
     * Process multi-agent comprehensive analysis
     */
    public CompletableFuture<CareerAssistanceResponse> processComprehensiveAnalysis(CareerAssistanceRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Execute agents in parallel with dependency management
                CompletableFuture<ResumeAnalysisAgent.ResumeAnalysisResult> resumeAnalysis =
                    CompletableFuture.supplyAsync(() ->
                        resumeAnalysisAgent.analyzeResume(
                            request.getResumeContent(),
                            request.getTargetRole(),
                            request.getExperienceLevel()), agentExecutor);

                CompletableFuture<JobMatchingAgent.JobMatchResult> jobMatching = resumeAnalysis
                    .thenCompose(resumeResult -> CompletableFuture.supplyAsync(() ->
                        jobMatchingAgent.calculateJobMatch(
                            request.getCandidateProfile(),
                            request.getJobDescription(),
                            request.getJobRequirements()), agentExecutor));

                CompletableFuture<CareerAdvisorAgent.CareerAdviceResult> careerAdvice =
                    CompletableFuture.allOf(resumeAnalysis, jobMatching)
                        .thenCompose(v -> CompletableFuture.supplyAsync(() ->
                            careerAdvisorAgent.provideCareerAdvice(
                                request.getCareerSituation(),
                                request.getCurrentRole(),
                                request.getCareerGoals(),
                                request.getTimeframe()), agentExecutor));

                // Aggregate results
                CompletableFuture.allOf(resumeAnalysis, jobMatching, careerAdvice).join();

                return CareerAssistanceResponse.comprehensive(
                    resumeAnalysis.join(),
                    jobMatching.join(),
                    careerAdvice.join(),
                    collectMetrics()
                );

            } catch (Exception e) {
                logger.error("Error in comprehensive analysis", e);
                return CareerAssistanceResponse.error("Comprehensive analysis failed: " + e.getMessage());
            }
        }, agentExecutor);
    }

    // Private agent creation methods
    private ResumeAnalysisAgent createResumeAnalysisAgent() {
        return AiServices.builder(ResumeAnalysisAgent.class)
                .chatLanguageModel(primaryModel)
                .tools(toolProvider)
                .chatMemory(MessageWindowChatMemory.withMaxMessages(20))
                .build();
    }

    private JobMatchingAgent createJobMatchingAgent() {
        return AiServices.builder(JobMatchingAgent.class)
                .chatLanguageModel(primaryModel)
                .tools(toolProvider)
                .chatMemory(MessageWindowChatMemory.withMaxMessages(15))
                .build();
    }

    private InterviewPrepAgent createInterviewPrepAgent() {
        return AiServices.builder(InterviewPrepAgent.class)
                .chatLanguageModel(primaryModel)
                .tools(toolProvider)
                .chatMemory(MessageWindowChatMemory.withMaxMessages(25))
                .build();
    }

    private CareerAdvisorAgent createCareerAdvisorAgent() {
        return AiServices.builder(CareerAdvisorAgent.class)
                .chatLanguageModel(primaryModel)
                .tools(toolProvider)
                .chatMemory(MessageWindowChatMemory.withMaxMessages(30))
                .build();
    }

    // Private processing methods
    private CareerAssistanceResponse processResumeAnalysis(CareerAssistanceRequest request) {
        ResumeAnalysisAgent.ResumeAnalysisResult result = resumeAnalysisAgent.analyzeResume(
            request.getResumeContent(),
            request.getTargetRole(),
            request.getExperienceLevel()
        );
        return CareerAssistanceResponse.resumeAnalysis(result);
    }

    private CareerAssistanceResponse processJobMatching(CareerAssistanceRequest request) {
        JobMatchingAgent.JobMatchResult result = jobMatchingAgent.calculateJobMatch(
            request.getCandidateProfile(),
            request.getJobDescription(),
            request.getJobRequirements()
        );
        return CareerAssistanceResponse.jobMatching(result);
    }

    private CareerAssistanceResponse processInterviewPrep(CareerAssistanceRequest request) {
        InterviewPrepAgent.InterviewEvaluationResult result = interviewPrepAgent.evaluateInterviewAnswer(
            request.getInterviewAnswer(),
            request.getInterviewQuestion(),
            request.getQuestionType(),
            request.getTargetRole()
        );
        return CareerAssistanceResponse.interviewPrep(result);
    }

    private CareerAssistanceResponse processCareerAdvice(CareerAssistanceRequest request) {
        CareerAdvisorAgent.CareerAdviceResult result = careerAdvisorAgent.provideCareerAdvice(
            request.getCareerSituation(),
            request.getCurrentRole(),
            request.getCareerGoals(),
            request.getTimeframe()
        );
        return CareerAssistanceResponse.careerAdvice(result);
    }

    private AgentMetrics collectMetrics() {
        return new AgentMetrics(
            System.currentTimeMillis(),
            agentMetrics.size(),
            "SUCCESS"
        );
    }

    // Getters for individual agents
    public ResumeAnalysisAgent getResumeAnalysisAgent() { return resumeAnalysisAgent; }
    public JobMatchingAgent getJobMatchingAgent() { return jobMatchingAgent; }
    public InterviewPrepAgent getInterviewPrepAgent() { return interviewPrepAgent; }
    public CareerAdvisorAgent getCareerAdvisorAgent() { return careerAdvisorAgent; }

    // Supporting classes
    public static class AgentMetrics {
        private final long timestamp;
        private final int activeAgents;
        private final String status;

        public AgentMetrics(long timestamp, int activeAgents, String status) {
            this.timestamp = timestamp;
            this.activeAgents = activeAgents;
            this.status = status;
        }

        // Getters
        public long getTimestamp() { return timestamp; }
        public int getActiveAgents() { return activeAgents; }
        public String getStatus() { return status; }
    }
}
