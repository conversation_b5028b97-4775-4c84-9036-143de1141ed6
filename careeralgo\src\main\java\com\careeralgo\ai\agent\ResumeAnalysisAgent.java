package com.careeralgo.ai.agent;

import dev.langchain4j.service.AiService;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;
import dev.langchain4j.service.MemoryId;
import reactor.core.publisher.Flux;

import java.util.concurrent.CompletableFuture;
import java.util.List;

/**
 * Modern AI agent for resume analysis using LangChain4j
 */
@AiService
public interface ResumeAnalysisAgent {

    @SystemMessage({
            "You are an expert resume analysis agent with deep knowledge in:",
            "- Resume optimization and ATS compatibility",
            "- Career development and professional branding",
            "- Industry-specific requirements and best practices",
            "- Skills assessment and gap analysis",
            "",
            "CAPABILITIES:",
            "- Analyze resume content for strengths and weaknesses",
            "- Provide ATS compatibility scores and recommendations",
            "- Suggest improvements for better job matching",
            "- Identify missing skills and experience gaps",
            "- Generate personalized enhancement suggestions",
            "",
            "ANALYSIS FRAMEWORK:",
            "- Content Quality: Clarity, relevance, and impact",
            "- Structure & Format: Organization and readability",
            "- ATS Optimization: Keyword usage and formatting",
            "- Professional Branding: Consistency and positioning",
            "- Skills Alignment: Match with target roles",
            "",
            "OUTPUT FORMAT:",
            "- Provide structured analysis with clear sections",
            "- Include numerical scores (0-100) for each category",
            "- List specific, actionable recommendations",
            "- Highlight top 3 strengths and top 3 improvement areas",
            "- Format responses in JSON when requested"
    })
    ResumeAnalysisResult analyzeResume(
            @UserMessage("Analyze this resume content: {{resumeContent}}") String resumeContent,
            @V("targetRole") String targetRole,
            @V("experienceLevel") String experienceLevel);

    @SystemMessage({
            "Perform comprehensive ATS (Applicant Tracking System) compatibility analysis.",
            "Focus on keyword optimization, formatting, and parsing compatibility.",
            "Provide specific recommendations for ATS improvement."
    })
    ATSCompatibilityResult analyzeATSCompatibility(
            @UserMessage String resumeContent,
            @V("jobDescription") String jobDescription);

    @SystemMessage({
            "Analyze skills mentioned in the resume and compare with industry requirements.",
            "Identify skill gaps and provide learning recommendations."
    })
    SkillsAnalysisResult analyzeSkills(
            @UserMessage String resumeContent,
            @V("targetIndustry") String targetIndustry,
            @V("targetRole") String targetRole);

    @SystemMessage({
            "Generate specific, actionable recommendations to improve the resume.",
            "Focus on content enhancement, formatting, and keyword optimization."
    })
    List<String> generateImprovementSuggestions(
            @UserMessage String resumeContent,
            @V("analysisResults") String analysisResults);

    @SystemMessage({
            "Stream real-time resume analysis updates as the analysis progresses.",
            "Provide incremental insights and progress updates."
    })
    Flux<AnalysisUpdate> streamResumeAnalysis(
            @UserMessage String resumeContent,
            @MemoryId String sessionId);

    @SystemMessage({
            "Compare multiple resume versions and provide improvement tracking.",
            "Highlight changes and their impact on overall quality."
    })
    CompletableFuture<ResumeComparisonResult> compareResumeVersions(
            @UserMessage("Compare these resume versions: {{versions}}") List<String> resumeVersions,
            @MemoryId String sessionId);

    @SystemMessage({
            "Generate a personalized resume enhancement plan based on career goals.",
            "Include timeline, priorities, and specific action items."
    })
    ResumeEnhancementPlan createEnhancementPlan(
            @UserMessage String resumeContent,
            @V("careerGoals") String careerGoals,
            @V("timeframe") String timeframe);

    // Supporting data structures
    record ResumeAnalysisResult(
            int overallScore,
            int contentQualityScore,
            int structureScore,
            int atsCompatibilityScore,
            int professionalBrandingScore,
            int skillsAlignmentScore,
            List<String> strengths,
            List<String> improvements,
            List<String> recommendations,
            String summary) {}

    record ATSCompatibilityResult(
            int atsScore,
            List<String> keywordMatches,
            List<String> missingKeywords,
            List<String> formattingIssues,
            List<String> recommendations) {}

    record SkillsAnalysisResult(
            List<String> identifiedSkills,
            List<String> missingSkills,
            List<String> skillGaps,
            List<String> learningRecommendations,
            int skillsMatchPercentage) {}

    record AnalysisUpdate(
            String updateType,
            String content,
            double progressPercentage,
            String timestamp) {}

    record ResumeComparisonResult(
            String improvementSummary,
            List<String> changes,
            int scoreImprovement,
            List<String> recommendations) {}

    record ResumeEnhancementPlan(
            List<String> priorities,
            List<String> actionItems,
            String timeline,
            List<String> resources,
            String expectedOutcome) {}
}
