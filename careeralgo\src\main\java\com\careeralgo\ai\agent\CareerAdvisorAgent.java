package com.careeralgo.ai.agent;

import dev.langchain4j.service.AiService;
import dev.langchain4j.service.SystemMessage;
import dev.langchain4j.service.UserMessage;
import dev.langchain4j.service.V;
import dev.langchain4j.service.MemoryId;
import reactor.core.publisher.Flux;

import java.util.concurrent.CompletableFuture;
import java.util.List;
import java.util.Map;

/**
 * Modern AI agent for comprehensive career guidance using LangChain4j
 */
@AiService
public interface CareerAdvisorAgent {

    @SystemMessage({
            "You are an expert career advisor agent with comprehensive knowledge in:",
            "- Career development and progression strategies",
            "- Industry trends and market dynamics",
            "- Skills development and learning pathways",
            "- Professional networking and personal branding",
            "- Work-life balance and career satisfaction",
            "",
            "ADVISORY EXPERTISE:",
            "- Career Transitions: Industry changes, role pivots, and skill transfers",
            "- Leadership Development: Management skills and executive presence",
            "- Entrepreneurship: Startup guidance and business development",
            "- Remote Work: Digital nomad lifestyle and remote career strategies",
            "- Diversity & Inclusion: Inclusive career advancement strategies",
            "",
            "GUIDANCE FRAMEWORK:",
            "- Assess current situation and career satisfaction",
            "- Identify strengths, interests, and values alignment",
            "- Analyze market opportunities and industry trends",
            "- Create actionable development plans with timelines",
            "- Provide ongoing support and progress tracking",
            "",
            "COMMUNICATION STYLE:",
            "- Empathetic and supportive approach",
            "- Data-driven insights with practical advice",
            "- Personalized recommendations based on individual goals",
            "- Encouraging while being realistic about challenges",
            "- Focus on long-term career satisfaction and growth"
    })
    CareerAdviceResult provideCareerAdvice(
            @UserMessage("Provide career advice for: {{careerSituation}}")
            String careerSituation,
            @V("currentRole") String currentRole,
            @V("careerGoals") String careerGoals,
            @V("timeframe") String timeframe);

    @SystemMessage({
            "Analyze career transition opportunities and provide strategic guidance.",
            "Consider transferable skills, market demand, and transition challenges."
    })
    CareerTransitionPlan analyzeCareerTransition(
            @UserMessage String currentCareerProfile,
            @V("targetIndustry") String targetIndustry,
            @V("targetRole") String targetRole,
            @V("motivations") String motivations);

    @SystemMessage({
            "Create a comprehensive professional development plan.",
            "Include skills development, networking, and career advancement strategies."
    })
    ProfessionalDevelopmentPlan createDevelopmentPlan(
            @UserMessage String professionalProfile,
            @V("careerObjectives") String careerObjectives,
            @V("developmentAreas") List<String> developmentAreas,
            @V("timeline") String timeline);

    @SystemMessage({
            "Analyze industry trends and provide market insights for career planning.",
            "Include growth opportunities, emerging roles, and skill demands."
    })
    IndustryAnalysisResult analyzeIndustryTrends(
            @UserMessage String targetIndustry,
            @V("geographicFocus") String geographicFocus,
            @V("timeHorizon") String timeHorizon);

    @SystemMessage({
            "Provide personalized networking strategy and professional branding advice.",
            "Include online presence, networking events, and relationship building."
    })
    NetworkingStrategy createNetworkingStrategy(
            @UserMessage String professionalProfile,
            @V("careerGoals") String careerGoals,
            @V("currentNetwork") String currentNetwork);

    @SystemMessage({
            "Stream real-time career coaching session with interactive guidance.",
            "Provide progressive insights and personalized recommendations."
    })
    Flux<CoachingUpdate> streamCareerCoaching(
            @UserMessage String careerChallenge,
            @V("context") String context,
            @MemoryId String sessionId);

    @SystemMessage({
            "Analyze work-life balance and provide strategies for career satisfaction.",
            "Consider personal values, lifestyle preferences, and career priorities."
    })
    CompletableFuture<WorkLifeBalanceAnalysis> analyzeWorkLifeBalance(
            @UserMessage String currentSituation,
            @V("personalValues") String personalValues,
            @V("lifeGoals") String lifeGoals,
            @MemoryId String userId);

    @SystemMessage({
            "Create a comprehensive career roadmap with milestones and action items.",
            "Include short-term and long-term goals with specific timelines."
    })
    CareerRoadmap createCareerRoadmap(
            @UserMessage String careerVision,
            @V("currentPosition") String currentPosition,
            @V("targetPosition") String targetPosition,
            @V("timeframe") String timeframe);

    @SystemMessage({
            "Provide salary negotiation strategy and market positioning advice.",
            "Include research methods, negotiation tactics, and value proposition."
    })
    SalaryNegotiationStrategy createNegotiationStrategy(
            @UserMessage String professionalProfile,
            @V("targetSalary") String targetSalary,
            @V("marketData") String marketData,
            @V("negotiationContext") String negotiationContext);

    // Supporting data structures
    record CareerAdviceResult(
            String adviceSummary,
            List<String> keyRecommendations,
            List<String> actionItems,
            List<String> resources,
            String timeline,
            List<String> potentialChallenges,
            String successMetrics) {}

    record CareerTransitionPlan(
            String transitionStrategy,
            List<String> transferableSkills,
            List<String> skillGaps,
            List<String> preparationSteps,
            String timeline,
            List<String> networkingTargets,
            String riskAssessment,
            List<String> mitigationStrategies) {}

    record ProfessionalDevelopmentPlan(
            String developmentStrategy,
            List<DevelopmentGoal> goals,
            List<String> learningResources,
            String networkingPlan,
            String mentorshipStrategy,
            String progressTracking) {}

    record DevelopmentGoal(
            String goal,
            String priority,
            String timeline,
            List<String> actionSteps,
            String successCriteria,
            List<String> resources) {}

    record IndustryAnalysisResult(
            String industryOverview,
            List<String> growthTrends,
            List<String> emergingRoles,
            List<String> inDemandSkills,
            List<String> challenges,
            List<String> opportunities,
            String marketOutlook,
            Map<String, String> salaryTrends) {}

    record NetworkingStrategy(
            String networkingApproach,
            List<String> targetConnections,
            List<String> networkingChannels,
            String personalBrandStrategy,
            List<String> contentStrategy,
            String engagementPlan,
            List<String> networkingEvents) {}

    record CoachingUpdate(
            String updateType,
            String guidance,
            String question,
            List<String> options,
            String insight,
            String timestamp) {}

    record WorkLifeBalanceAnalysis(
            String balanceAssessment,
            List<String> stressFactors,
            List<String> satisfactionAreas,
            List<String> improvementStrategies,
            String lifestyleRecommendations,
            String careerAdjustments) {}

    record CareerRoadmap(
            String visionStatement,
            List<CareerMilestone> milestones,
            List<String> criticalSuccessFactors,
            String developmentPlan,
            String contingencyPlanning) {}

    record CareerMilestone(
            String milestone,
            String timeframe,
            List<String> requirements,
            List<String> actionItems,
            String successMetrics) {}

    record SalaryNegotiationStrategy(
            String negotiationApproach,
            String valueProposition,
            List<String> researchFindings,
            List<String> negotiationTactics,
            String fallbackStrategy,
            List<String> preparationSteps,
            String timingAdvice) {}
}
