package com.careeralgo.service;

import com.careeralgo.model.Application;
import com.careeralgo.model.User;
import com.careeralgo.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Service for sending email notifications
 * TODO: Integrate with SendGrid or similar email service
 */
@Service
public class EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    @Value("${careeralgo.email.enabled:false}")
    private boolean emailEnabled;

    @Value("${careeralgo.email.from-address:<EMAIL>}")
    private String fromAddress;

    @Autowired
    private UserRepository userRepository;

    /**
     * Send offer received notification
     */
    public void sendOfferReceivedNotification(String userId, Application application) {
        if (!emailEnabled) {
            logger.debug("Email notifications disabled, skipping offer notification");
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null || !user.getPreferences().isEmailNotifications()) {
                logger.debug("User not found or email notifications disabled for user: {}", userId);
                return;
            }

            // TODO: Implement actual email sending with SendGrid
            logger.info("Would send offer notification email to: {} for application: {}", 
                    user.getEmail(), application.getId());
            
            // Placeholder for email content
            String subject = "🎉 You received a job offer!";
            String content = buildOfferEmailContent(user, application);
            
            // TODO: Send actual email
            // sendGridService.sendEmail(user.getEmail(), subject, content);
            
        } catch (Exception e) {
            logger.error("Failed to send offer notification email", e);
        }
    }

    /**
     * Send interview reminder notification
     */
    public void sendInterviewReminderNotification(String userId, Application application) {
        if (!emailEnabled) {
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null || !user.getPreferences().isEmailNotifications()) {
                return;
            }

            logger.info("Would send interview reminder email to: {} for application: {}", 
                    user.getEmail(), application.getId());
            
            String subject = "📅 Interview Reminder";
            String content = buildInterviewReminderContent(user, application);
            
            // TODO: Send actual email
            
        } catch (Exception e) {
            logger.error("Failed to send interview reminder email", e);
        }
    }

    /**
     * Send weekly application summary
     */
    public void sendWeeklyApplicationSummary(String userId) {
        if (!emailEnabled) {
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null || !user.getPreferences().isWeeklyReports()) {
                return;
            }

            logger.info("Would send weekly summary email to: {}", user.getEmail());
            
            String subject = "📊 Your Weekly Job Search Summary";
            String content = buildWeeklySummaryContent(user);
            
            // TODO: Send actual email
            
        } catch (Exception e) {
            logger.error("Failed to send weekly summary email", e);
        }
    }

    /**
     * Send job alert notification
     */
    public void sendJobAlertNotification(String userId, String jobTitle, int jobCount) {
        if (!emailEnabled) {
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null || !user.getPreferences().isJobAlerts()) {
                return;
            }

            logger.info("Would send job alert email to: {} for {} new jobs", 
                    user.getEmail(), jobCount);
            
            String subject = String.format("🔔 %d new %s jobs found", jobCount, jobTitle);
            String content = buildJobAlertContent(user, jobTitle, jobCount);
            
            // TODO: Send actual email
            
        } catch (Exception e) {
            logger.error("Failed to send job alert email", e);
        }
    }

    /**
     * Send welcome email to new user
     */
    public void sendWelcomeEmail(String userId) {
        if (!emailEnabled) {
            return;
        }

        try {
            User user = userRepository.findById(userId).orElse(null);
            if (user == null) {
                return;
            }

            logger.info("Would send welcome email to: {}", user.getEmail());
            
            String subject = "🚀 Welcome to CareerAlgo!";
            String content = buildWelcomeEmailContent(user);
            
            // TODO: Send actual email
            
        } catch (Exception e) {
            logger.error("Failed to send welcome email", e);
        }
    }

    // Email content builders

    private String buildOfferEmailContent(User user, Application application) {
        return String.format("""
            Hi %s,
            
            Congratulations! You've received a job offer for your application.
            
            Application Details:
            - Application ID: %s
            - Status: %s
            
            Please log in to your CareerAlgo dashboard to view the full offer details.
            
            Best regards,
            The CareerAlgo Team
            """, 
            user.getFirstName(), 
            application.getId(), 
            application.getStatus());
    }

    private String buildInterviewReminderContent(User user, Application application) {
        return String.format("""
            Hi %s,
            
            This is a reminder about your upcoming interview.
            
            Application Details:
            - Application ID: %s
            - Status: %s
            
            Please log in to your CareerAlgo dashboard to view interview details and preparation materials.
            
            Good luck!
            The CareerAlgo Team
            """, 
            user.getFirstName(), 
            application.getId(), 
            application.getStatus());
    }

    private String buildWeeklySummaryContent(User user) {
        return String.format("""
            Hi %s,
            
            Here's your weekly job search summary:
            
            This week you:
            - Applied to X jobs
            - Received Y responses
            - Had Z interviews
            
            Keep up the great work! Log in to CareerAlgo to see your detailed analytics.
            
            Best regards,
            The CareerAlgo Team
            """, 
            user.getFirstName());
    }

    private String buildJobAlertContent(User user, String jobTitle, int jobCount) {
        return String.format("""
            Hi %s,
            
            We found %d new %s jobs that match your preferences!
            
            Log in to CareerAlgo to view these opportunities and apply with one click.
            
            Happy job hunting!
            The CareerAlgo Team
            """, 
            user.getFirstName(), 
            jobCount, 
            jobTitle);
    }

    private String buildWelcomeEmailContent(User user) {
        return String.format("""
            Hi %s,
            
            Welcome to CareerAlgo! We're excited to help you accelerate your career journey.
            
            Here's what you can do next:
            1. Complete your profile
            2. Upload your resume
            3. Start exploring job opportunities
            4. Get AI-powered recommendations
            
            Get started: https://careeralgo.com/dashboard
            
            If you have any questions, don't hesitate to reach out to our support team.
            
            Welcome aboard!
            The CareerAlgo Team
            """, 
            user.getFirstName());
    }
}
